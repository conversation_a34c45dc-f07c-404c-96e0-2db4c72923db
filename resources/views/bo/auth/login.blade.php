@extends('bo.layouts.master-without-nav')

@section('body')

<body id="app" class="authentication-bg">
    @endsection

    @section('content')
    <div class="account-pages mt-5 mb-5">
        <div class="container">

            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6 col-xl-5">

                    <div class="card bg-pattern">

                        <div class="card-body p-4">

                            <div class="text-center w-75 m-auto">
                                <a href="/">
                                    @if(date('m')==11)
                                    <img src="{{asset('images/diya.gif')}}" height="50">
                                    @endif
                                    @if(date('m')==12)
                                    <img src="{{asset('images/christmas-tree.gif')}}" height="50">
                                    @endif
                                    <span><img
                                            src="https://www.cardbaazi.com/templates/assets/images/CardBaazi-Logo.svg"
                                            alt="" height="80"></span>
                                </a>
                                <p class="text-muted mb-4 mt-3">Enter your email address and password to access admin
                                    panel.</p>
                            </div>

                            <form method="POST" action="{{ route('login') }}">
                                @csrf
                                <div class="form-group mb-3">
                                    <label for="email">Email address</label>
                                    <input class="form-control @error('email') is-invalid @enderror" type="text"
                                        id="email" name="login" required autocomplete="email" autofocus
                                        placeholder="Enter your email" value="{{ old('email') }}">
                                    @error('email')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                    @enderror
                                </div>

                                <div class="form-group mb-3">
                                    <label for="password">Password</label>
                                    <input class="form-control @error('password') is-invalid @enderror" type="password"
                                        name="password" required autocomplete="current-password" id="password"
                                        placeholder="Enter your password">
                                    @error('password')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                    @enderror
                                </div>

                                {{-- <div class="form-group mb-3">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="checkbox-signin" checked>
                                    <label class="custom-control-label" for="checkbox-signin">Remember me</label>
                                </div>
                            </div> --}}

                                <div class="form-group mb-0 text-center">
                                    <button class="btn btn-primary btn-block" type="submit"> Log In </button>
                                </div>

                            </form>

                        </div> <!-- end card-body -->

                    </div>

                    <!-- end card -->
                </div> <!-- end col -->
            </div>
            <!-- end row -->
        </div>
        <!-- end container -->
    </div>
    <!-- end page -->


    <footer class="footer footer-alt">
        {{ date('Y') }} &copy; Cardbaazi
    </footer>
    @endsection