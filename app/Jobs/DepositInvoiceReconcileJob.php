<?php

namespace App\Jobs;
use App\Http\Controllers\bo\transaction\PaymentSearchController;
use App\Models\DepositGstInvoice;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DepositInvoiceReconcileJob implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;
    protected $data;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $invoiceData = $this->data['invoiceDataArr'];
        
        $isAlreadyProcessed = !empty(DepositGstInvoice::where('INTERNAL_REFERENCE_NO', $invoiceData['INTERNAL_REFERENCE_NO'])->first());
        if(!$isAlreadyProcessed){
            $newInoviceNo = (new PaymentSearchController())->generateDepositInvoiceNo($invoiceData['CREATED_DATE']);
            $invoiceData['INVOICE_NO'] = $newInoviceNo;
            DepositGstInvoice::create($invoiceData);
        }
    }
}