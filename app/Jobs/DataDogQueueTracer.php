<?php

namespace App\Jobs;

use Illuminate\Queue\Jobs\JobName;
use function DDTrace\trace_method;
use DDTrace\SpanData;
use Illuminate\Contracts\Queue\Job;

class DataDogQueueTracer
{
    public function register(): void
    {
        if (!extension_loaded('ddtrace')) {
            return;
        }

        trace_method(
            'Illuminate\Queue\Worker',
            'process',
            function (SpanData $span, array $args) {
                /** @var string $connectionName */
                /** @var Job $job */
                [$connectionName, $job, $options] = $args;
                $payload = $job->payload();

                $span->resource = JobName::resolve($job->getName(), $payload);

                // you can change these values below
                $span->name = 'laravel.queue.worker';
                $span->type = 'workers';
                $span->service = 'queue-workers'; // will show up as APM's service

                $span->meta = [
                    'jobPayload' => json_encode($payload),
                    'connection' => $connectionName,
                    'queue' => $job->getQueue(),
                ];
            }
        );
    }
}
