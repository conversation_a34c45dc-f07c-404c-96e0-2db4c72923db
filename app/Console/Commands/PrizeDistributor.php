<?php

namespace App\Console\Commands;

use Exception;
use App\Models\Leaderboard;
use Illuminate\Console\Command;
use App\Models\LeaderboardPrize;
use App\Models\LeaderboardPoints;
use App\Models\MasterLeaderboard;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Constants\LeaderboardConstant;
use App\Http\Resources\Leaderboard\LeaderboardPointResource;
use App\Models\helpdesk\MasterTransactionHistory;
use App\Models\UserPoint;
use App\Jobs\KinesisStreamDataPushJob;
use App\Models\CampaignToUser;
use App\Models\MasterTransactionHistoryFppBonus;
use App\Models\MasterTransactionHistoryRewardpoints;
use App\Models\Stake;
use App\Models\User;
use App\Traits\Models\common_methods;
use Carbon\Carbon;

class PrizeDistributor extends Command
{
    use common_methods;
    private $leaderboardIdentifier = "PLB";
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'prizedistributor:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Responsible for distributing Prize';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $leaderboards = Leaderboard::where(function($query){
                            $query->where(function($query){
                                $query->completedLeaderboards();
                            })
                            ->orWhere(function($query){
                                $query->FailedLeaderboards();
                            });
                        })
                        ->where('IS_ACTIVE', 1)
                        ->where("END_DATE", ">=", date('Y-m-d H:i:s', strtotime(date('Y-m-d H:i:s')  . " - 3 days")) )
                        ->get();
        foreach ($leaderboards as $leaderboard) {
            try {
                $this->distributePrize($leaderboard);
            }
            catch(Exception $ex){
                Log::error("Error While Prize Distributor Process for Leaderboard: $leaderboard->LEADERBOARD_ID " . date('Y-m-d H:i:s') . $ex);
            }
        }
    }

    public function distributePrize($leaderboard){
        
        try {
            DB::beginTransaction();
                /**
                 * Get Reward Type Data
                 */
                $rewardTypeData = \DB::table('reward_types')
                    ->where('REWARD_TYPE_ID', $leaderboard->REWARD_TYPE_ID)
                    ->first();

                $prizeType = empty($rewardTypeData) ? $leaderboard->PRIZE_TYPE : $rewardTypeData->COIN_TYPE_ID;
                $amountType = empty($rewardTypeData) ? $leaderboard->AMOUNT_TYPE : $rewardTypeData->BALANCE_TYPE_ID;

                $leaderboardRank = LeaderboardPoints::where('LEADERBOARD_ID', $leaderboard->LEADERBOARD_ID)
                                ->with('user')
                                ->orderBy('LEADERBOARD_POINTS', 'DESC')
                                ->orderBy('LEADERBOARD_POINT_ID', 'ASC')
                                ->limit($leaderboard->NUMBER_OF_WINNERS)
                                ->get();
                if ($leaderboardRank->count() > 0) {
                    $topWinners = LeaderboardPointResource::customCollection($leaderboardRank,$leaderboardRank->count(),$leaderboard->NUMBER_OF_WINNERS,$leaderboard->TOTAL_PRIZE_POOL, $leaderboardRank->sum('LEADERBOARD_POINTS'));
                    if (count($topWinners) > 0) {
                        $stakeList = Stake::select('STAKE_NAME', 'MIN_STAKE', 'MAX_STAKE', 'STAKE_ID')->where([['STAKE_ID', $leaderboard->STAKE_ID], ['STATUS', 1]])->first();
                        foreach ($topWinners as $winningUser) {
                            $internalReferenceNumber = $this->createReferenceNumber($winningUser->USER_ID);
                            $userPoints = UserPoint::where('USER_ID', $winningUser->USER_ID)
                                    ->where('COIN_TYPE_ID', $prizeType)
                                    ->lockForUpdate()
                                    ->first();
                            $userClosingTOTBalance = $userPoints ? ($userPoints->USER_TOT_BALANCE +  $winningUser->PRIZE_MONEY) : $winningUser->PRIZE_MONEY;
                            // Insert into Master Transaction History
                            $mthData = [
                                'USER_ID' => $winningUser->USER_ID,
                                'BALANCE_TYPE_ID' => $amountType,
                                'TRANSACTION_STATUS_ID' => 107,
                                'TRANSACTION_TYPE_ID' => 7,
                                'TRANSACTION_AMOUNT' => $winningUser->PRIZE_MONEY,
                                'TRANSACTION_DATE' => date('Y-m-d H:i:s'),
                                'INTERNAL_REFERENCE_NO' => $internalReferenceNumber,
                                'CURRENT_TOT_BALANCE' => $userPoints ? $userPoints->USER_TOT_BALANCE : 0,
                                'CLOSING_TOT_BALANCE' => $userClosingTOTBalance,
                                'PARTNER_ID' => $winningUser->user->PARTNER_ID,
                                'OPERATION_TYPE' => null,
                                'TRANSACTION_ID' => null
                            ];
                            // Create Leaderboard Prize
                            LeaderboardPrize::create([
                                'LEADERBOARD_ID'=> $leaderboard->LEADERBOARD_ID,
                                'LEADERBOARD_POINT_ID' => $winningUser->LEADERBOARD_POINT_ID,
                                'USER_ID'   => $winningUser->USER_ID,
                                'RANKS'  => $winningUser->RANKS,
                                'PRIZE_TYPE' => $prizeType,
                                'PRIZE_AMOUNT' => $winningUser->PRIZE_MONEY,
                                'INTERNAL_REFERENCE_NO' => $internalReferenceNumber,
                            ]);
                            switch($prizeType) {
                                case 3:
                                    $campaiganTouser = CampaignToUser::create([
                                        'PROMO_CAMPAIGN_ID' => $leaderboard->LEADERBOARD_ID,
                                        'USER_ID' => $winningUser->USER_ID,
                                        'CAMPAIGN_CODE' => 'Leaderboard',
                                        'CAMPAIGN_NAME' => 'Leaderboard',
                                        'BONUSE_TYPE' => 'Locked',
                                        'BONUS_AMOUNT' => $winningUser->PRIZE_MONEY,
                                        'BALANCE_BONUS_AMOUNT' => $winningUser->PRIZE_MONEY,
                                        'BONUS_RELEASE_PER' => $leaderboard->CONVERSION,
                                        'REDEEM_DATE' => Carbon::now(),
                                        'EXPIRY_DATE' => Carbon::now()->addDays($leaderboard->VALIDITY),
                                        'BONUS_STATUS' => 1,
                                        'STATUS' => 1,
                                        'COIN_TYPE_ID' => $prizeType
                                    ]);

                                    DB::table('special_bonus_meta_data')->insert([
                                        'CAMPAIGN_TO_USER_ID' => $campaiganTouser->CAMPAIGN_TO_USER_ID,
                                        'SOURCE' => 'Leaderboard',
                                        'META_DATA' => json_encode([
                                            'title' => $leaderboard->NAME,
                                            'game'  => $leaderboard->game->GAME_NAME,
                                            "game_sub_type" => $leaderboard->gameType->GAME_TYPE ?? '',
                                        ])
                                    ]);

                                    MasterTransactionHistoryFppBonus::create($mthData);
                                    break;
                                case 12:
                                    MasterTransactionHistoryRewardpoints::create($mthData);
                                    break;
                                default:
                                    MasterTransactionHistory::create($mthData);
                            }

                            // Calculate User Points for Update
                            $userDepositBalance = $userPoints->USER_DEPOSIT_BALANCE;
                            $userPromoBalance = $userPoints->USER_PROMO_BALANCE;
                            $userWinBalance = $userPoints->USER_WIN_BALANCE;
                            switch($amountType){
                                case 1: // Deposit Balance
                                    $userDepositBalance = $userPoints->USER_DEPOSIT_BALANCE +  $winningUser->PRIZE_MONEY;
                                    break;
                                case 2: // Promo Balance
                                    $userPromoBalance = $userPoints->USER_PROMO_BALANCE +  $winningUser->PRIZE_MONEY;
                                    break;
                                case 3: // Win Balance
                                    $userWinBalance = $userPoints->USER_WIN_BALANCE +  $winningUser->PRIZE_MONEY;
                                    break;
                            }
                            // Update User Points
                            UserPoint::where('USER_ID', $winningUser->USER_ID)
                                    ->where('COIN_TYPE_ID', $prizeType)
                                    ->update([
                                        'VALUE' => $winningUser->PRIZE_MONEY,
                                        'USER_DEPOSIT_BALANCE' => $userDepositBalance,
                                        'USER_PROMO_BALANCE' => $userPromoBalance,
                                        'USER_WIN_BALANCE' => $userWinBalance,
                                        'USER_TOT_BALANCE' => $userClosingTOTBalance
                                    ]);
                            $user = User::find($winningUser->USER_ID);
                            if($user):
                                $ctData = [
                                    "type"	 	=> "event",
                                    "type_name" => "Leaderboard Winnings",
                                    "data" => [
                                                "identity" => $winningUser->USER_ID,
                                                "Game" => $leaderboard->game->GAME_NAME,
                                                "Variant" => $leaderboard->gameType->GAME_TYPE ?? '',
                                                "Stake" => $stakeList ? $stakeList->STAKE_NAME ." - ". $stakeList->MIN_STAKE." - ". $stakeList->MAX_STAKE." - ".$leaderboard->game->GAME_NAME : '',
                                                "User Prize" => $winningUser->PRIZE_MONEY,
                                                "Rank of user" => $winningUser->RANKS,
                                                "UserId" => $winningUser->USER_ID,
                                                "Mobile number" => $user->CONTACT,
                                                "Email id" => $user->EMAIL_ID,
                                                "Leaderboard name" => $leaderboard->NAME,
                                                "Leaderboard ID" => $leaderboard->LEADERBOARD_ID,
                                                "Leaderboard total winners" => $leaderboard->NUMBER_OF_WINNERS,
                                                "Leaderboard total prize" => $leaderboard->TOTAL_PRIZE_POOL,
                                                "Prize Type" => $prizeType,
                                                "Reward Type" => $rewardTypeData->REWARD_NAME <> 'Locked Bonus' ? ($rewardTypeData->REWARD_NAME == 'Reward Point' && $winningUser->PRIZE_MONEY >1 ? 'Reward Points' : $rewardTypeData->REWARD_NAME) : 'Special Bonus',
                                                "Leaderboard Start Date" => $leaderboard->START_DATE?date('Y-m-d H:i:s', strtotime($leaderboard->START_DATE)):'',
                                                "Leaderboard End Date" => $leaderboard->END_DATE?date('Y-m-d H:i:s', strtotime($leaderboard->END_DATE)):''
                                            ]
                                ];

                                $leaderboardType = [
                                    '',
                                    'Daily',
                                    'Weekly',
                                    'Monthly'
                                ];

                                $dataForRealTimeNotification = [
                                    "leaderboard_name" => $leaderboard->game->GAME_NAME,
                                    "leaderboard_type" => $leaderboardType[$leaderboard->INTERVAL_TYPE] ?? '',
                                    "leaderboard_descripton" => $leaderboard->DESCRIPTION,
                                    "reward_type" => $rewardTypeData->REWARD_NAME <> 'Locked Bonus' ? ($rewardTypeData->REWARD_NAME == 'Reward Point' && $winningUser->PRIZE_MONEY >1 ? 'Reward Points' : $rewardTypeData->REWARD_NAME) : 'Special Bonus',
                                    "reward_value" => $winningUser->PRIZE_MONEY
                                ];

                                $this->sendDataToCardbaaziRealTimeEventsTable($winningUser->USER_ID, "Leaderboard", $dataForRealTimeNotification);
                                try {
                                    KinesisStreamDataPushJob::dispatch($ctData)->onConnection('tour_sqs');
                                } catch (\Exception $e) {
                                    Log::error($e->getMessage());
                                }
                                
                                
                            endif;
                
                        }
                    }
                }
                $this->markLeaderboardCompleted($leaderboard);
            DB::commit();
        }
        catch(Exception $ex){
            DB::rollBack();
            Leaderboard::setFailed($leaderboard->LEADERBOARD_ID, LeaderboardConstant::STATUS_FAILED);
            Log::error("Error While Distributing the Prize - " . date('Y-m-d H:i:s') . " - For Leaderboard ID " . $leaderboard->LEADERBOARD_ID . "; Exception :" . $ex);
        }
    }

    private function getDistributedMoney($totaWinners = 1, $totalPrizePool){
        return $totalPrizePool / ($totaWinners);
    }

    private function createReferenceNumber($userId){
        return $this->leaderboardIdentifier . $userId . date('dmyhis') . rand(0,9999);
    }
    private function markLeaderboardCompleted($leaderboard){
        if($leaderboard->INTERVAL_TYPE == LeaderboardConstant::INTERVAL_CUSTOM){
            MasterLeaderboard::setStatus($leaderboard->LEADERBOARD_ID, LeaderboardConstant::STATUS_PRIZE_DISTRIBUTED);
            MasterLeaderboard::setCompleted($leaderboard->LEADERBOARD_ID, LeaderboardConstant::COMPLETED);
        }
        Leaderboard::setStatus($leaderboard->LEADERBOARD_ID, LeaderboardConstant::STATUS_PRIZE_DISTRIBUTED);
        Leaderboard::setCompleted($leaderboard->LEADERBOARD_ID, LeaderboardConstant::COMPLETED);
    }
}
