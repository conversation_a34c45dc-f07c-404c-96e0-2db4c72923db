<?php

namespace App\Console\Commands;

use Exception;
use Carbon\Carbon;
use App\Models\Games;
use App\Models\Leaderboard;
use Illuminate\Console\Command;
use App\Models\MasterLeaderboard;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Constants\LeaderboardConstant;
use App\Jobs\SendLeaderboardEndCTJob;

class LeaderboardDumper extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'leaderboarddumper:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command Responsible For Dumping into Child Leaderboard Table from Master Leaderboard';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $leaderboards = MasterLeaderboard::activeLeaderboards()
                                            ->where('IS_ACTIVE', 1)
                                            ->get();
            foreach ($leaderboards as $masLeaderboard) {
                $this->createLeaderboardBasedOnIntervalType($masLeaderboard);
            }
            DB::commit();
        }
        catch(Exception $ex){
            DB::rollBack();
            Log::error("Error While Leaderboard Dumper" . date('Y-m-d H:i:s') . $ex);
        }
    }

    /**
     * Transform Data of Master Leaderboard for Leaderboard Model
     *
     * @param [MASTER LEADERBOARD MODEL] $masterLeaderboard
     * @return array
     * <AUTHOR> Kewat <<EMAIL>>
     */
    private function transformMasterLeaderboardToLeaderboard($masterLeaderboard, $start_date = null, $end_date = null, $display_start_date = null, $displayStartDateGap = 0)
    {
        $start_date = $start_date ?? $masterLeaderboard->START_DATE;
        return [
            'MASTER_LEADERBOARD_ID' => $masterLeaderboard->MASTER_LEADERBOARD_ID,
            'GAME_TABLE_NAME'       =>  getTableName($masterLeaderboard->GAME_ID, $masterLeaderboard->GAME_SUB_TYPE_ID),
            'NAME'                  => $masterLeaderboard->NAME,
            'DESCRIPTION'           => $masterLeaderboard->DESCRIPTION,
            'GAME_ID'               => $masterLeaderboard->GAME_ID,
            'GAME_SUB_TYPE_ID'      => $masterLeaderboard->GAME_SUB_TYPE_ID,
            'GAME_TYPE_ID'          => $masterLeaderboard->GAME_TYPE_ID,
            'STAKE_ID'              => $masterLeaderboard->STAKE_ID,
            'IS_COMPLETED'          => $masterLeaderboard->IS_COMPLETED,
            'STATUS'                => LeaderboardConstant::STATUS_TO_BE_STARTED,
            'START_DATE'            => $start_date,
            'END_DATE'              => $end_date ?? $masterLeaderboard->END_DATE,
            'INTERVAL_TYPE'         => $masterLeaderboard->INTERVAL_TYPE,
            'DISPLAY_START_DATE'    => Carbon::createFromDate($start_date)->subSecond($displayStartDateGap)->format("Y-m-d H:i:s"),
            'DISPLAY_END_DATE'      => $display_end_date ?? $masterLeaderboard->DISPLAY_END_DATE,
            'DISPLAY_DATE'          => $display_start_date ?? $masterLeaderboard->DISPLAY_DATE,
            'POINT_CRITERIA'        => $masterLeaderboard->POINT_CRITERIA,
            'REWARD_TYPE_ID'        => $masterLeaderboard->REWARD_TYPE_ID,
            'VALIDITY'              => $masterLeaderboard->VALIDITY,
            'CONVERSION'            => $masterLeaderboard->CONVERSION,
            'PRIZE_TYPE'            => $masterLeaderboard->PRIZE_TYPE,
            'TOTAL_PRIZE_POOL'      => $masterLeaderboard->TOTAL_PRIZE_POOL,
            'HIGHLIGHTER_TAG'       => $masterLeaderboard->HIGHLIGHTER_TAG,
            'AMOUNT_TYPE'           => $masterLeaderboard->AMOUNT_TYPE,
            'NUMBER_OF_WINNERS'     => $masterLeaderboard->NUMBER_OF_WINNERS,
            'UPDATED_BY'            => $masterLeaderboard->UPDATED_BY,
            'MINUTE_FOR_LEADERBOARD_END_CT_EVENT' => $masterLeaderboard->MINUTE_FOR_LEADERBOARD_END_CT_EVENT,
            'POINT_PERCENTAGE' => $masterLeaderboard->POINT_PERCENTAGE
        ];
    }

    /**
     * Create Leaderboard Based on Interval Type of Master Leaderboard
     *
     * @param [MASTER LEADERBOARD MODEL] $leaderboard
     * @return void
     * <AUTHOR> Kewat <<EMAIL>>
     */
    private function createLeaderboardBasedOnIntervalType($masLeaderboard)
    {
        $startTime = Carbon::createFromTimeString(substr($masLeaderboard->START_DATE, 11, 19));
        $endTime = Carbon::createFromTimeString(substr($masLeaderboard->END_DATE, 11, 19));
        $displayStartDateGap =  (Carbon::createFromDate($masLeaderboard->START_DATE))->diffInSeconds(Carbon::createFromDate($masLeaderboard->DISPLAY_START_DATE)) ?? 0;
        $isSameDate = $startTime->lt($endTime);
        if ($masLeaderboard->INTERVAL_TYPE === LeaderboardConstant::INTERVAL_CUSTOM) {
            $existingLeaderboard = Leaderboard::getExistingLeaderboard($masLeaderboard->MASTER_LEADERBOARD_ID, $masLeaderboard->START_DATE, $masLeaderboard->END_DATE);
            $this->checkAlreadyExistingLeaderboards($masLeaderboard, $masLeaderboard->START_DATE);
            if (!isset($existingLeaderboard)) {
                $leaderboard = Leaderboard::create($this->transformMasterLeaderboardToLeaderboard($masLeaderboard, null, null, null, $displayStartDateGap));
            }
        } else if ($masLeaderboard->INTERVAL_TYPE === LeaderboardConstant::INTERVAL_DAILY) {
            $start_date = $this->createUpcomingDatesFromDate($masLeaderboard->START_DATE, "+ 1");
            $end_date = $this->createUpcomingDatesFromDate($masLeaderboard->END_DATE,  $isSameDate ? "+ 1" : "+ 2");
            // If Master Leaderboard End Date is less then new Start date then dont create leaderboard
            if($end_date > $masLeaderboard->END_DATE || $start_date  <= $masLeaderboard->START_DATE){
                return true;
            }
            $existingLeaderboard = Leaderboard::getExistingLeaderboard($masLeaderboard->MASTER_LEADERBOARD_ID, $start_date, $end_date);
            $this->checkAlreadyExistingLeaderboards($masLeaderboard, $start_date);
            if (!isset($existingLeaderboard)) {
                $display_start_date = $this->createUpcomingDatesFromDate($start_date, "- 1");
                $leaderboard = Leaderboard::create($this->transformMasterLeaderboardToLeaderboard($masLeaderboard, $start_date, $end_date, $display_start_date, $displayStartDateGap));
            }
        } else if ($masLeaderboard->INTERVAL_TYPE === LeaderboardConstant::INTERVAL_WEEKLY) {
            $start_date = $this->createUpcomingDatesFromDate($masLeaderboard->START_DATE, "+ 7");
            $days = $this->getDifferenceInDays($masLeaderboard->START_DATE, $start_date);
            // Create Next Leaderboard when new start date differnce with main start date is divisible by 7
            if ($days % 7 == 0) {
                $end_date = $this->createUpcomingDatesFromDate($masLeaderboard->END_DATE,  $isSameDate ? "+ 7" : "+ 8");
                // If Master Leaderboard End Date is less then new Start date then dont create leaderboard
                if($end_date > $masLeaderboard->END_DATE || $start_date  <= $masLeaderboard->START_DATE){
                    return true;
                }
                $existingLeaderboard = Leaderboard::getExistingLeaderboard($masLeaderboard->MASTER_LEADERBOARD_ID, $start_date, $end_date);
                $this->checkAlreadyExistingLeaderboards($masLeaderboard, $start_date);
                if (!isset($existingLeaderboard)) {
                    $display_start_date = $this->createUpcomingDatesFromDate($start_date, "- 1");
                    $leaderboard = Leaderboard::create($this->transformMasterLeaderboardToLeaderboard($masLeaderboard, $start_date, $end_date, $display_start_date, $displayStartDateGap));
                }
            }
        } else if ($masLeaderboard->INTERVAL_TYPE === LeaderboardConstant::INTERVAL_MONTHLY) {
            $start_date = $this->createUpcomingDatesFromDate($masLeaderboard->START_DATE, "+ 31");
            $days = $this->getDifferenceInDays($masLeaderboard->START_DATE, $start_date);
            // Create Next Leaderboard when new start date differnce with main start date is divisible by 30
            if ($days % 30 == 0) {
                $end_date = $this->createUpcomingDatesFromDate($masLeaderboard->END_DATE,  $isSameDate ? "+ 31" : "+ 32");
                // If Master Leaderboard End Date is less then new Start date then dont create leaderboard
                if($end_date > $masLeaderboard->END_DATE || $start_date  <= $masLeaderboard->START_DATE){
                    return true;
                }
                $existingLeaderboard = Leaderboard::getExistingLeaderboard($masLeaderboard->MASTER_LEADERBOARD_ID, $start_date, $end_date);
                $this->checkAlreadyExistingLeaderboards($masLeaderboard, $start_date);
                if (!isset($existingLeaderboard)) {
                    $display_start_date = $this->createUpcomingDatesFromDate($start_date, "- 1");
                    $leaderboard = Leaderboard::create($this->transformMasterLeaderboardToLeaderboard($masLeaderboard, $start_date, $end_date, $display_start_date, $displayStartDateGap));
                }
            }
        }

        if(!empty($masLeaderboard->MINUTE_FOR_LEADERBOARD_END_CT_EVENT) && isset($leaderboard) && !empty($leaderboard)) {
            //Dispach Job before end of leaderboard fro leaderboard end CT
            $time = Carbon::parse($end_date ?? $masLeaderboard->END_DATE)->subMinutes($masLeaderboard->MINUTE_FOR_LEADERBOARD_END_CT_EVENT);

            SendLeaderboardEndCTJob::dispatch($leaderboard->LEADERBOARD_ID, $leaderboard->STAKE_ID, $leaderboard->REWARD_TYPE_ID)->delay($time)->allOnConnection('database');
        }
    }

    /**
     * This Function will create a next date from current date and will append passed date's time to make start date or end date
     *
     * @param [date] $date
     * @param [Integer] $daysToAdd
     * @return date
     * <AUTHOR> Kewat <<EMAIL>>
     */
    private function createUpcomingDatesFromDate($date, $daysToAdd)
    {
        return date('Y-m-d H:i:s', strtotime(date(Carbon::now()->format('Y-m-d') . substr($date, 11, 8)) . " $daysToAdd days"));
    }

    private function getDifferenceInDays($firstDate, $secondDate)
    {
        $datediff = strtotime($secondDate) - strtotime($firstDate);
        return round($datediff / (60 * 60 * 24));
    }

    private function checkAlreadyExistingLeaderboards($masLeaderboard, $start_date){
        $leaderboards = Leaderboard::where('MASTER_LEADERBOARD_ID', $masLeaderboard->MASTER_LEADERBOARD_ID)
                    ->whereDate('START_DATE', date("Y-m-d", strtotime($start_date)))
                    ->where('STATUS', '!=', LeaderboardConstant::STATUS_UNKNOWN)
                    ->orderBy('CREATED_AT', "ASC")
                    ->get();
        if(count($leaderboards) > 1){
            $leaderboards = Leaderboard::where('MASTER_LEADERBOARD_ID', $masLeaderboard->MASTER_LEADERBOARD_ID)
                    ->whereDate('START_DATE', date("Y-m-d", strtotime($start_date)))
                    ->where('STATUS', '!=', LeaderboardConstant::STATUS_UNKNOWN)
                    ->where('LEADERBOARD_ID', '!=', $leaderboards->last()->LEADERBOARD_ID)
                    ->update([
                        'IS_COMPLETED' => 1,
                        'STATUS' => LeaderboardConstant::STATUS_UNKNOWN
                    ]);
        }
    }
}
