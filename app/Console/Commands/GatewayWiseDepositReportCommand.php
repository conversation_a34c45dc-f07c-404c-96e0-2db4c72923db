<?php

namespace App\Console\Commands;

use App\Http\Controllers\bo\reports\GatewayWiseDepositAutomationReportController;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GatewayWiseDepositReportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'deposit-report {frequency}';

    /**
     * The console command description.
     *
     * @var string->withoutOverlapping()
     */
    protected $description = 'Automated Gatewaywise Deposit Report';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
       
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try{
            $frequency = $this->argument('frequency');
            $ReportController = new GatewayWiseDepositAutomationReportController(); 
            $ReportController->getReport($frequency);     

        }catch(\Exception $e){
            Log::error($e);
        }        
    }
}
