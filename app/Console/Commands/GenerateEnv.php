<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class GenerateEnv extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate-env {sec_name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate env from json';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
      
        #Local
        # Run this first : aws secretsmanager get-secret-value   --secret-id new-bo-qa --query SecretString > cred.json
        //$filePath=base_path()."/cred.json";
        //$content=stripslashes(trim(str_replace("\n", "",file_get_contents($filePath)),'"'));

        #qa/pord
        $sec_name=$this->argument('sec_name');
        $content=stripslashes(trim(str_replace("\n", "",shell_exec('aws secretsmanager get-secret-value   --secret-id '.$sec_name.' --query SecretString')),'"'));

        $content=json_decode($content,true);
        //dd($content); exit;

        $envfile = fopen(base_path()."/.env1", "w") or die("Unable to open file!");
        foreach($content as $key=>$val){

            if($key=="FIREBASE_JSON"){
                $fb_json_file = fopen(base_path()."/firebase_credentials1.json", "w") or die("Unable to open file!");
                fwrite($fb_json_file, $val);
                continue;
            }
            $text=$key.'="'.$val.'"'.PHP_EOL;
            fwrite($envfile, $text);
        }
        fclose($envfile);

        #Delete existing env file and renamning env1 to env
        if(file_exists(base_path()."/.env1") ){

            if(file_exists(base_path()."/.env"))
                unlink(base_path()."/.env");

            rename(base_path()."/.env1",base_path()."/.env");

        }
        if(file_exists(base_path()."/firebase_credentials1.json") ){

            if(file_exists(base_path()."/firebase_credentials.json"))
                unlink(base_path()."/firebase_credentials.json");

            rename(base_path()."/firebase_credentials1.json",base_path()."/firebase_credentials.json");

        }

        #Clearing config cache
        \Artisan::call('config:cache');
        return $this->info(".env generated successfully and config cache cleared");
    }
}