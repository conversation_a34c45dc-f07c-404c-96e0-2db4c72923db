<?php

namespace App\Console\Commands;

use Exception;
use App\Models\Leaderboard;
use App\Factories\GameDataFactory;
use Illuminate\Console\Command;
use App\Models\LeaderboardPoints;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use App\Jobs\SendLeaderboardRankedNotification;

class RankGenerator extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rankgenerator:cron {--leaderboardId=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $conditions = [];
            if($this->option('leaderboardId')){
                $conditions = ['LEADERBOARD_ID' => $this->option('leaderboardId')];
            }

            $leaderboards = Leaderboard::activeLeaderboards()
                                    ->where('IS_ACTIVE', 1)
                                    ->where($conditions)
                                    ->get();
            foreach ($leaderboards as $leaderboard) {
                $oldRanks = $this->getRanks($leaderboard->LEADERBOARD_ID);
                $gameHistory = $this->mapTableData(
                    $this->getAllUsersPlayedGamesData($leaderboard->GAME_TABLE_NAME, $leaderboard->GAME_ID, $leaderboard->GAME_SUB_TYPE_ID, $leaderboard->START_DATE, $leaderboard->END_DATE, $leaderboard->stake)
                );
                if ($gameHistory &&  $gameHistory->count() > 0){
                    $userWithSortedBuyIns = $gameHistory->where('BUY_IN', '>', 0)->sortByDesc('BUY_IN');

                    $usersPoints = $this->createUserPoints($userWithSortedBuyIns, $leaderboard);
                    // Reset Redis Key for that Leaderboard Points
                    Redis::del($this->getKeyForRedisLeaderboard($leaderboard));                   
                    foreach($usersPoints as $userPoint){
                        if($userPoint['LEADERBOARD_POINTS'] == 0){
                            continue;
                        }
                        $leaderBoardPoints = LeaderboardPoints::updateOrInsert([
                            'LEADERBOARD_ID'     => $userPoint['LEADERBOARD_ID'],
                            'USER_ID'            => $userPoint['USER_ID']
                        ],$userPoint);
                        // Set the Leaderboard Points in the Redis
                        Redis::lpush($this->getKeyForRedisLeaderboard($leaderboard), json_encode($userPoint));
                    }

                }
                $newRanks = $this->getRanks($leaderboard->LEADERBOARD_ID);
                try {
                    SendLeaderboardRankedNotification::dispatch($oldRanks, $newRanks, $leaderboard)->onConnection('tour_sqs');
                } catch (\Exception $e) {
                    Log::error($e->getMessage());
                }
                
            }
            DB::commit();
            
        }
        catch(Exception $ex){
            DB::rollBack();
            Log::error("Error While Rank Generator Process" . date('Y-m-d H:i:s') . $ex);
        }
    }

    /**
     * Get Users Played Games Data From Specified Table and In the Range of Start Date and End Date
     *
     * @param [type] $tableName
     * @param [type] $gameId
     * @param [type] $subGameTypeId
     * @param [type] $startDateTime
     * @param [type] $endDateTime
     * @param [Stake Model] $stakes
     * @return Collection
     */
    private function getAllUsersPlayedGamesData($tableName, $gameId = null, $subGameTypeId = null, $startDateTime, $endDateTime, $stakes)
    {
        // DB::connection()->enableQueryLog();
        $gameDataColumns = (new GameDataFactory())->getGameData($gameId, $subGameTypeId);
        $userIdColumn = $gameDataColumns[0];
        $selectBuyInColumn = $gameDataColumns[1];
        $selectBuyOutColumn = $gameDataColumns[2];
        $buyInColumn = $gameDataColumns[3];
        $buyOutColumn = $gameDataColumns[4];
        $createdAtColumn = $gameDataColumns[5];
        $updatedAtColumn = $gameDataColumns[6];
        $extraStaticConditions = $gameDataColumns[7];
        $extraGroupBy = $gameDataColumns[8];
        $extraOrderBy = $gameDataColumns[9];
        $minStake = $stakes->MIN_STAKE ?? null;
        $maxStake = $stakes->MAX_STAKE ?? null;
        //     Update Select Query if we have different Condition on BUY IN COLUMN like  'SUM(CASE WHEN TRANSACTION_TYPE_ID = 13 THEN TRANSACTION_AMOUNT ELSE 0 END)';
        //     Update Select Query if we have different Condition on BUY OUT COLUMN like  $this->buyOutColumn = 'SUM(CASE WHEN TRANSACTION_TYPE_ID = 71 THEN TRANSACTION_AMOUNT ELSE 0 END)';
        $data = DB::table($tableName)
                        ->selectRaw("$userIdColumn as USER_ID, $selectBuyInColumn as BUY_IN, $selectBuyOutColumn as BUY_OUT, $createdAtColumn as CREATED_AT, $updatedAtColumn as UPDATED_AT")
                        ->whereBetween("$updatedAtColumn", [$startDateTime,$endDateTime])
                        ->when(!empty($extraStaticConditions), function($query) use($extraStaticConditions){
                            $query->whereRaw($extraStaticConditions);
                        })
                        ->when(isset($minStake) && isset($maxStake), function($query) use($buyInColumn, $minStake, $maxStake){
                            $query->whereBetween("$buyInColumn", [$minStake, $maxStake]);
                        })
                        ->when($extraGroupBy, function($query) use($extraGroupBy){
                            $query->groupByRaw($extraGroupBy);
                        })
                        ->when( ! empty($extraOrderBy) , function($query) use($extraOrderBy){
                            foreach($extraOrderBy as $orderByColumn => $orderByDirection){
                                $query->orderBy($orderByColumn, $orderByDirection);
                            }
                        })
                        ->get();
        // Logging the queries on QA for testing purpose
        // Log::info(DB::getQueryLog());
        // DB::connection()->disableQueryLog();
        return $data;
    }

    /**
     * Map Data
     *
     * @param [type] $tableData
     * @return Collection
     */
    private function mapTableData($tableData){
        return $tableData->map(function ($record) {
            return collect($record)->only(['USER_ID', 'BUY_IN', 'BUY_OUT']);
        });
    }

    private function createUserPoints($userWithSortedBuyIns, $leaderboard){
        $userPoints = [];
        $pointCalculationType = config('rummy_config.IS_LEADERBOARD_POINT_CALCULATION_BASED_ON_WAGGERED_AMOUNT');

        $oneWagerPoint = $pointCalculationType ? (floatval($leaderboard->stake->CONVERSION_POINT) /  floatval($leaderboard->stake->WAGERED_AMOUNT)) : 1;

        $userWithSortedBuyIns = $userWithSortedBuyIns->map(function ($value) use($oneWagerPoint) {
            $value['BUY_IN'] = round(floatval($value['BUY_IN'] * $oneWagerPoint), 2);
            return $value;
        });

        $totalPointsGeneratedByTopWinners = $userWithSortedBuyIns->take($leaderboard->NUMBER_OF_WINNERS)->sum('BUY_IN');
        // if($leaderboard->POINT_PERCENTAGE && ($leaderboard->POINT_PERCENTAGE > 0 && $leaderboard->POINT_PERCENTAGE <= 100))
        // {
        //     $totalPointsGeneratedByTopWinners = (int)ceil($totalPointsGeneratedByTopWinners * $leaderboard->POINT_PERCENTAGE /100);
        // }
        $totalWinners = $userWithSortedBuyIns->count() > $leaderboard->NUMBER_OF_WINNERS ? $leaderboard->NUMBER_OF_WINNERS : $userWithSortedBuyIns->count();
        $winnersPercentage = $totalWinners / $leaderboard->NUMBER_OF_WINNERS;
        $totalPrizePool = $winnersPercentage * $leaderboard->TOTAL_PRIZE_POOL;
        $totalPrizePool = floor(($totalPrizePool*100))/100;

        $previousRank = $rank = 1;
        $previousPoints = 0;
        $displayRankToCount = (int)ceil(
            $leaderboard->NUMBER_OF_WINNERS + (config('rummy_config.EXTRA_RANK_PERCENTAGE_TO_SHOW_IN_LEADERBOARD_RANK') * $leaderboard->NUMBER_OF_WINNERS
            )
        );
        $index = 1;

        foreach ($userWithSortedBuyIns as $userBuyInData) {
            $userPoint = $userBuyInData['BUY_IN'];
            if($leaderboard->POINT_PERCENTAGE && ($leaderboard->POINT_PERCENTAGE > 0 && $leaderboard->POINT_PERCENTAGE <= 100))
            {
                /**
                 * case 1 - If totalPointsGeneratedByTopWinners < rakeExpected(100000) 
                 * rakeExpected 100000 = 10000/(10/100);
                 * totalWinnings 50 = 500/100000*10000;
                 * 
                 * case 2 - totalPointsGeneratedByTopWinners > rakeExpected(100000)
                 * rakeExpected 100000 = 10000/(10/100);
                 * totalWinnings 7000 = 70000/100000*10000;
                 * extraPrize 12000 = 120000*10/100;
                 * extraWinnings 1166.66 = 7000/12000*(12000-10000);
                 * totalWinnings 5833.34 = 7000-1166.66;
                 */
                $rakeExpected = $totalPrizePool/($leaderboard->POINT_PERCENTAGE/100);
                $totalWinnings = floor(($userPoint/$rakeExpected*$totalPrizePool)*100)/100;
                if($totalPointsGeneratedByTopWinners > $rakeExpected){
                    $extraPrize = floor($totalPointsGeneratedByTopWinners*$leaderboard->POINT_PERCENTAGE/100);
                    $extraWinnings = $totalWinnings/$extraPrize*($extraPrize-$totalPrizePool);
                    $totalWinnings = floor(($totalWinnings - $extraWinnings)*100)/100;
                }
            } else {
                $totalWinnings = floor((($userPoint * $totalPrizePool) / $totalPointsGeneratedByTopWinners)*100)/100;
            }

            $userRank = ($previousPoints != $userPoint) ? $rank : $previousRank;
            
            array_push($userPoints, [
                'LEADERBOARD_ID'     => $leaderboard->LEADERBOARD_ID,
                'USER_ID'            => $userBuyInData['USER_ID'],
                'LEADERBOARD_POINTS' => $userPoint,
                'TOTAL_WINNINGS'     => $index <= $leaderboard->NUMBER_OF_WINNERS ? $totalWinnings : null,
                'RANKS'              => $index <= $displayRankToCount ? $userRank : null
            ]);

            $previousRank = ($previousPoints != $userPoint) ? $rank : $previousRank;
            $rank = $rank+1;
            $previousPoints = $userPoint;
            $index++;
        }
        return $userPoints;
    }

    private function getKeyForRedisLeaderboard($leaderboard){
        return "LeaderboardPoints:$leaderboard->LEADERBOARD_ID";
    }

    private function getLeaderboardPointsFromRedis($leaderboard){
        foreach(Redis::lrange($this->getKeyForRedisLeaderboard($leaderboard),0, -1) as $leaderboardPoints => $value){
            echo $value;
        }
    }

    private function getRanks($leaderboardId)
    {
        return LeaderboardPoints::where('LEADERBOARD_ID', $leaderboardId)->orderBy('LEADERBOARD_POINTS','DESC')->get();
    }
}
