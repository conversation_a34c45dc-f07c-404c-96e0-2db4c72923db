<?php

namespace App\Console\Commands;

use App\Traits\s3Bucket;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class addRedirectionIdToBannerJsonForOldVideos extends Command
{
    /**
     * This method use to upload file in to S3Bucket
     * @class app/traits/s3bucket
     * @method s3FileUpload
     * @param $file , $filePath, $allowedFormats, $hasFile
     */
    use s3Bucket;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update-banner:update_banner_data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Banner Data';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->jsonFiles = config('rummy_config.JSON_FILE_NAME_FOR_UPDATING_REDIRECTION_ID');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {                        
        if (!empty($this->jsonFiles)) {
            foreach ($this->jsonFiles as $files) {
                $filePath = config('rummy_config.cardBaaziAllMobileAppsS3Path') . '/' . $files;

                $fileExist = Storage::disk('s3')->exists($filePath);
                if ($fileExist) {
                    $videosFile = Storage::disk('s3')->get($filePath);
                    $videosFile = json_decode($videosFile);
                    $s3bannerVideos = $videosFile;

                    if (isset($videosFile->videos)) {
                        $s3bannerVideos = $videosFile->videos;

                        if (isset($s3bannerVideos->items)) {
                            $items = $s3bannerVideos->items;

                            foreach ($items as $videoFile) {
                                $redirectionParams = $videoFile->redirectionParams;
                                if (isset($redirectionParams) && !isset($redirectionParams->redirectionId)) {
                                    $urlParams = parse_url($redirectionParams->redirectionUrl);

                                    /**
                                     * Added Video id For Playing Videos in mini player
                                     */
                                    $youtubeId = '';

                                    if (is_array($urlParams) && isset($urlParams['path'])) {
                                        $youtubeId = str_contains($urlParams['path'], 'shorts/') ? explode('shorts/', $urlParams['path'])[1] : explode('v=', $urlParams['query'])[1];
                                        $redirectionParams->redirectionId = $youtubeId;
                                    }
                                }
                            }
                        }
                    }

                    try {
                        $videosFile->videos = $s3bannerVideos;
                        Storage::disk('s3')->put($filePath, json_encode($videosFile), 'public');
                    } catch (\Exception $exception) {
                        Log::error($exception);
                    }
                }
            }
        }
    }
}
