<?php

namespace App\Console\Commands;

use Exception;
use Carbon\Carbon;
use App\Models\Games;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Constants\SLTournamentConstant;
use App\Jobs\SendLeaderboardEndCTJob;
use App\Jobs\SendTournamentRankEventJob;
use App\Models\MasterSNLTournament;
use App\Models\SNLTournament;
use App\Models\SNLTournamentsBoardConfig;

class SNLTournamentDumper extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'snltournamentdumper:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command Responsible For Dumping into Child Tournament Table from Master Tournament';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $tournaments = MasterSNLTournament::activeTournaments()
                                            ->where('IS_ACTIVE', 1)
                                            ->get();
            foreach ($tournaments as $masTournament) {
                $this->createTournamentBasedOnIntervalType($masTournament);
            }
            DB::commit();
        }
        catch(Exception $ex){
            DB::rollBack();
            Log::error("Error While SNL Tournament Dumper" . date('Y-m-d H:i:s') . $ex);
        }
    }

    /**
     * Transform Data of Master Tournament for Tournament Model
     *
     * @param [MASTER Tournament MODEL] $masterTournament
     * @return array
     * <AUTHOR> Sharma <<EMAIL>>
     */
    private function transformMasterTournamentToTournament($masterTournament, $start_date = null, $end_date = null, $display_start_date = null, $displayStartDateGap = 0, $boardId = null)
    {
        $boardId = !empty($boardId) ? $boardId : $masterTournament->BOARD_ID;
        $boardData = SNLTournamentsBoardConfig::where('board_id', $boardId)->first();
        $boardConfiguration = json_decode($boardData->board_configuration, true);
        
        $gameConfig = $boardConfiguration['staticData']['gameConfig'];
        
        $snakesData = array_keys($gameConfig['snakeConfig']);
        $laddersData = array_keys($gameConfig['ladders']);
        
        $positionToIgnore = array_merge($snakesData, $laddersData, [100]);
        $allPositions = [];
        $tokenColors = SLTournamentConstant::TOKEN_COLORS;


        // Initialize tokens array
        $tokens = [];

        // Iterate over each row from 2nd to 10th
        for ($row = 2; $row <= 10; $row++) {
            // Exclude positions already occupied by snakes or ladders
            for ($i = $row * 10 - 9; $i <= $row * 10; $i++) {
                if (!in_array($i, $positionToIgnore)) {
                    $allPositions[$row ][] = $i;
                }
            }
        }
        shuffle($allPositions);
        $rowValue = 0;
        foreach($tokenColors as $key => $value){
            for($i=1; $i<=3; $i++){
                $randomKey =  array_rand($allPositions[$rowValue]) ;
                $score = $position =  $allPositions[$rowValue][$randomKey];
                $tokens[$key][$i] = [
                    'score' => $key != "red" ? $score : 0,
                    'position' => $key != "red" ? $position : 0
                ];
                if($key != "red"){
                    unset($allPositions[$rowValue][$randomKey]);
                    $rowValue = $rowValue + 1;
                }
                
            }
        }
       
        $boardConfiguration['staticData']['tokens'] = $tokens;
        
        $otherData = json_decode($masterTournament->OTHER_DATA, true);
        $otherData['BOARD_CONFIG'] = $boardConfiguration;

        $start_date = $start_date ?? $masterTournament->START_DATE;
        return [
            'MASTER_SNL_TOURNAMENT_ID' => $masterTournament->MASTER_SNL_TOURNAMENT_ID,
            'NAME'                  => $masterTournament->NAME,
            'DESCRIPTION'           => $masterTournament->DESCRIPTION,
            'GAME_ID'               => $masterTournament->GAME_ID,
            'TOURNAMENT_FORMAT'     => $masterTournament->TOURNAMENT_FORMAT,
            'TOURNAMENT_TYPE'       => $masterTournament->TOURNAMENT_TYPE,
            'BUY_IN'                => $masterTournament->BUY_IN,
            'INTERVAL_TYPE'         => $masterTournament->INTERVAL_TYPE,
            'RECURSION_TYPE'        => $masterTournament->RECURSION_TYPE,
            'RECURSION_VALUE'       => $masterTournament->RECURSION_VALUE,
            'START_DATE'            => $start_date,
            'END_DATE'              => $end_date ?? $masterTournament->END_DATE,
            'DISPLAY_START_DATE'    => Carbon::createFromDate($start_date)->subSecond($displayStartDateGap)->format("Y-m-d H:i:s"),
            'REWARD_TYPE_ID'        => $masterTournament->REWARD_TYPE_ID,
            'VALIDITY'              => $masterTournament->VALIDITY,
            'CONVERSION'            => $masterTournament->CONVERSION,
            'TOTAL_PRIZE_POOL'      => $masterTournament->TOTAL_PRIZE_POOL,
            'MINUTE_FOR_TOURNAMENT_END_CT_EVENT' => $masterTournament->MINUTE_FOR_TOURNAMENT_END_CT_EVENT,
            'RAKE'                  => $masterTournament->RAKE,
            'PERCENTAGE_OF_WINNERS' => $masterTournament->PERCENTAGE_OF_WINNERS,
            'NUMBER_OF_WINNERS' => $masterTournament->NUMBER_OF_WINNERS,
            'TOTAL_ENTRIES'         => $masterTournament->TOTAL_ENTRIES,
            'MINIMUM_ENTRIES'       => $masterTournament->MINIMUM_ENTRIES,
            'TOTAL_ENTRIES_PER_USER'=> $masterTournament->TOTAL_ENTRIES_PER_USER,
            'BOARD_ID'              => $boardId,
            'TAG'                   => $masterTournament->TAG,
            'HIGHLIGHT'             => $masterTournament->HIGHLIGHT,
            'PIN_TO_TOP'            => $masterTournament->PIN_TO_TOP,
            'IS_COMPLETED'          => $masterTournament->IS_COMPLETED,
            'STATUS'                => SLTournamentConstant::STATUS_TO_BE_STARTED,
            'IS_ACTIVE'             => $masterTournament->IS_ACTIVE,
            'OTHER_DATA'            => json_encode($otherData),
            'CREATED_BY'            => $masterTournament->CREATED_BY,
            'UPDATED_BY'            => $masterTournament->UPDATED_BY,
        ];
    }

    /**
     * Create Tournament Based on Interval Type of Master Tournament
     *
     * @param [MASTER TOURNAMENT MODEL] $tournament
     * @return void
     * <AUTHOR> Sharma <<EMAIL>>
     */
    private function createTournamentBasedOnIntervalType($masTournament)
    {
        $startTime = Carbon::createFromTimeString(substr($masTournament->START_DATE, 11, 19));
        $endTime = Carbon::createFromTimeString(substr($masTournament->END_DATE, 11, 19));
        $displayStartDateGap =  (Carbon::createFromDate($masTournament->START_DATE))->diffInSeconds(Carbon::createFromDate($masTournament->DISPLAY_START_DATE)) ?? 0;
        $isSameDate = $startTime->lt($endTime);

        if ($masTournament->INTERVAL_TYPE === SLTournamentConstant::INTERVAL_CUSTOM) {
            $existingTournament = SNLTournament::getExistingTournament($masTournament->MASTER_SNL_TOURNAMENT_ID, $masTournament->START_DATE, $masTournament->END_DATE);
            $this->checkAlreadyExistingTournaments($masTournament, $masTournament->START_DATE);
            if (!isset($existingTournament)) {
                $tournament = SNLTournament::create($this->transformMasterTournamentToTournament($masTournament, null, null, null, $displayStartDateGap));
            }
        } else if ($masTournament->INTERVAL_TYPE === SLTournamentConstant::INTERVAL_DAILY) {
            $start_date = $this->createUpcomingDatesFromDate($masTournament->START_DATE, "+ 1");
            $end_date = $this->createUpcomingDatesFromDate($masTournament->END_DATE,  $isSameDate ? "+ 1" : "+ 2");
            // If Master Tournament End Date is less then new Start date then dont create Tournament
            
            if($end_date > $masTournament->END_DATE || $start_date  <= $masTournament->START_DATE){
                return true;
            }
            $existingTournament = SNLTournament::getExistingTournament($masTournament->MASTER_SNL_TOURNAMENT_ID, $start_date, $end_date);
            $this->checkAlreadyExistingTournaments($masTournament, $start_date);
            if (!isset($existingTournament)) {
                $display_start_date = $this->createUpcomingDatesFromDate($start_date, "- 1");
                $tournament = SNLTournament::create($this->transformMasterTournamentToTournament($masTournament, $start_date, $end_date, $display_start_date, $displayStartDateGap));
            }
        } else if ($masTournament->INTERVAL_TYPE === SLTournamentConstant::INTERVAL_WEEKLY) {
            $start_date = $this->createUpcomingDatesFromDate($masTournament->START_DATE, "+ 7");
            $days = $this->getDifferenceInDays($masTournament->START_DATE, $start_date);
            // Create Next Tournament when new start date differnce with main start date is divisible by 7
            if ($days % 7 == 0) {
                $end_date = $this->createUpcomingDatesFromDate($masTournament->END_DATE,  $isSameDate ? "+ 7" : "+ 8");
                // If Master Tournament End Date is less then new Start date then dont create Tournament
                if($end_date > $masTournament->END_DATE || $start_date  <= $masTournament->START_DATE){
                    return true;
                }
                $existingTournament = SNLTournament::getExistingTournament($masTournament->MASTER_SNL_TOURNAMENT_ID, $start_date, $end_date);
                $this->checkAlreadyExistingTournaments($masTournament, $start_date);
                if (!isset($existingTournament)) {
                    $display_start_date = $this->createUpcomingDatesFromDate($start_date, "- 1");
                    $tournament = SNLTournament::create($this->transformMasterTournamentToTournament($masTournament, $start_date, $end_date, $display_start_date, $displayStartDateGap));
                }
            }
        } else if ($masTournament->INTERVAL_TYPE === SLTournamentConstant::INTERVAL_MONTHLY) {
            $start_date = $this->createUpcomingDatesFromDate($masTournament->START_DATE, "+ 31");
            $days = $this->getDifferenceInDays($masTournament->START_DATE, $start_date);
            // Create Next Tournament when new start date differnce with main start date is divisible by 30
            if ($days % 30 == 0) {
                $end_date = $this->createUpcomingDatesFromDate($masTournament->END_DATE,  $isSameDate ? "+ 31" : "+ 32");
                // If Master Tournament End Date is less then new Start date then dont create Tournament
                if($end_date > $masTournament->END_DATE || $start_date  <= $masTournament->START_DATE){
                    return true;
                }
                $existingTournament = SNLTournament::getExistingTournament($masTournament->MASTER_SNL_TOURNAMENT_ID, $start_date, $end_date);
                $this->checkAlreadyExistingTournaments($masTournament, $start_date);
                if (!isset($existingTournament)) {
                    $display_start_date = $this->createUpcomingDatesFromDate($start_date, "- 1");
                    $tournament = SNLTournament::create($this->transformMasterTournamentToTournament($masTournament, $start_date, $end_date, $display_start_date, $displayStartDateGap));
                }
            }
        } else if ($masTournament->INTERVAL_TYPE === SLTournamentConstant::INTERVAL_RECURRING) {
            $recurringType = $masTournament->RECURSION_TYPE;
            $recurringValue = $masTournament->RECURSION_VALUE;
            $lastChildTournament = SNLTournament::where('MASTER_SNL_TOURNAMENT_ID', $masTournament->MASTER_SNL_TOURNAMENT_ID)->orderBy('SNL_TOURNAMENT_ID','DESC')->first();
            $startTimeRec = Carbon::parse($masTournament->START_DATE)->format('H:i');
            $endTimeRec = Carbon::parse($masTournament->END_DATE)->format('H:i');
            $currentTime = Carbon::now()->addMinutes(15)->format('H:i');
            $lastChildTournamentEndDate = $lastChildTournament->END_DATE;
            
            if($recurringType == 1){
                $timeGap = Carbon::parse(Carbon::now()->format('Y-m-d H:i:s'))->diffInMinutes($lastChildTournamentEndDate);
                if($timeGap > $recurringValue && Carbon::now()->format('Y-m-d') > Carbon::parse($lastChildTournamentEndDate)->format('Y-m-d')){
                    $lastChildTournamentEndDate = Carbon::parse(Carbon::now()->toDateString()." ".Carbon::parse($masTournament->START_DATE)->format('H:i:s'));
                }
                $start_date = Carbon::parse($lastChildTournamentEndDate)->addMinute()->toDateTimeString();
                $end_date = Carbon::parse($lastChildTournamentEndDate)->addMinutes($recurringValue)->toDateTimeString();
            } else {
                $timeGap = Carbon::parse(Carbon::now()->format('Y-m-d H:i:s'))->diffInHours($lastChildTournamentEndDate);
                if($timeGap > $recurringValue  && Carbon::now()->format('Y-m-d') > Carbon::parse($lastChildTournamentEndDate)->format('Y-m-d')){
                    $lastChildTournamentEndDate = Carbon::parse(Carbon::now()->toDateString()." ".Carbon::parse($masTournament->START_DATE)->format('H:i:s'));
                }
                $start_date = Carbon::parse($lastChildTournamentEndDate)->addMinute()->toDateTimeString();
                $end_date = Carbon::parse($lastChildTournamentEndDate)->addHours($recurringValue)->toDateTimeString();
            }
            if($end_date <= $masTournament->END_DATE && $currentTime >= $startTimeRec && $currentTime < $endTimeRec && Carbon::now()->addMinutes(20)->toDateTimeString() >= $start_date){
                $display_start_date = $this->createUpcomingDatesFromDate($start_date, "- 1");
                $boardIds = SNLTournamentsBoardConfig::pluck('board_id')->toArray();
                $boardId = in_array($lastChildTournament->BOARD_ID + 1, $boardIds) ? $lastChildTournament->BOARD_ID + 1 : $boardIds[0];
                $tournament = SNLTournament::create($this->transformMasterTournamentToTournament($masTournament, $start_date, $end_date, $display_start_date, $displayStartDateGap, $boardId));
            }
        }

        if(!empty($masTournament->MINUTE_FOR_TOURNAMENT_END_CT_EVENT) && isset($tournament) && !empty($tournament)) {
            //Dispach Job before end of tournament for tournament end CT
            $time = Carbon::parse($tournament->END_DATE)->subMinutes($tournament->MINUTE_FOR_TOURNAMENT_END_CT_EVENT);
            SendTournamentRankEventJob::dispatch($tournament->SNL_TOURNAMENT_ID)->delay($time)->allOnConnection('database');
        }
    }

    /**
     * This Function will create a next date from current date and will append passed date's time to make start date or end date
     *
     * @param [date] $date
     * @param [Integer] $daysToAdd
     * @return date
     * <AUTHOR> Kewat <<EMAIL>>
     */
    private function createUpcomingDatesFromDate($date, $daysToAdd)
    {
        return date('Y-m-d H:i:s', strtotime(date(Carbon::now()->format('Y-m-d') . substr($date, 11, 8)) . " $daysToAdd days"));
    }

    private function getDifferenceInDays($firstDate, $secondDate)
    {
        $datediff = strtotime($secondDate) - strtotime($firstDate);
        return round($datediff / (60 * 60 * 24));
    }

    private function checkAlreadyExistingTournaments($masTournament, $start_date){
        $tournaments = SNLTournament::where('MASTER_SNL_TOURNAMENT_ID', $masTournament->MASTER_SNL_TOURNAMENT_ID)
                    ->whereDate('START_DATE', date("Y-m-d", strtotime($start_date)))
                    ->where('STATUS', '!=', SLTournamentConstant::STATUS_UNKNOWN)
                    ->orderBy('CREATED_AT', "ASC")
                    ->get();
        if(count($tournaments) > 1){
            $tournaments = SNLTournament::where('MASTER_SNL_TOURNAMENT_ID', $masTournament->MASTER_SNL_TOURNAMENT_ID)
                    ->whereDate('START_DATE', date("Y-m-d", strtotime($start_date)))
                    ->where('STATUS', '!=', SLTournamentConstant::STATUS_UNKNOWN)
                    ->where('SNL_TOURNAMENT_ID', '!=', $tournaments->last()->SNL_TOURNAMENT_ID)
                    ->update([
                        'IS_COMPLETED' => 1,
                        'STATUS' => SLTournamentConstant::STATUS_UNKNOWN
                    ]);
        }
    }
}
