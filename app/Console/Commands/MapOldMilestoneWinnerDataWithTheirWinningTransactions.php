<?php

namespace App\Console\Commands;

use App\Models\helpdesk\MasterTransactionHistory;
use App\Models\MasterTransactionHistoryFpbonus;
use App\Models\MasterTransactionHistoryRewardpoints;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MapOldMilestoneWinnerDataWithTheirWinningTransactions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'map-milestone-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Map Milestone Data With Transactions';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            DB::table('milestones_winner as mw')
                ->leftJoin('milestones as m', 'm.MILESTONE_ID', 'mw.MILESTONE_ID')
                ->select('REWARD_TYPE_ID', DB::raw('DATE_FORMAT(mw.UPDATE_DATE, "%Y-%m-%d %H:%i") as UPDATE_DATE'), 'REWARD_VALUE', 'USER_ID', 'MILESTONE_WINNER_ID')
                ->whereNull('INTERNAL_REFERENCE_NO')
                ->where('IS_CLAIMED', 1)
                ->orderBy('MILESTONE_WINNER_ID', 'desc')
                ->chunk(100, function ($data) {
                    $this->mapMilestoneWinnerDataWithTheirTransaction($data);
                });

            $this->info("Milestone Winner Data Mapped Succesfully With Their Transaction");
        } catch (\Exception $e) {
            Log::error($e);
            $this->error("Something Went Wrong, Please Try Again Later");
        }
    }

    /**
     * function to map milestone winner data with their transaction
     * 
     * @param $data
     * @return void 
     */
    private function mapMilestoneWinnerDataWithTheirTransaction($data)
    {
        try {
            foreach ($data as $milestoneWinnerData) {
                $transactionDate = $milestoneWinnerData->UPDATE_DATE;
                switch ($milestoneWinnerData->REWARD_TYPE_ID) {
                    case 3:
                        $transactionData = MasterTransactionHistoryFpbonus::query();
                        break;
                    case 12:
                        $transactionData = MasterTransactionHistoryRewardpoints::query();
                        break;
                    default:
                        $transactionData = MasterTransactionHistory::query();
                }

                $transactionData = $transactionData->where('USER_ID', $milestoneWinnerData->USER_ID)
                    ->where('TRANSACTION_DATE', 'like', "$transactionDate%")
                    ->where('TRANSACTION_AMOUNT', "$milestoneWinnerData->REWARD_VALUE.00")
                    ->whereIn('TRANSACTION_TYPE_ID', [222, 223])
                    ->select('INTERNAL_REFERENCE_NO', 'TRANSACTION_DATE')
                    ->first();

                if (!empty($transactionData)) {
                    DB::table('milestones_winner')->where('MILESTONE_WINNER_ID', $milestoneWinnerData->MILESTONE_WINNER_ID)->update([
                        'INTERNAL_REFERENCE_NO' => $transactionData->INTERNAL_REFERENCE_NO,
                        'UPDATE_DATE' => $transactionData->TRANSACTION_DATE
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error($e);
            $this->error("Something Went Wrong, Please Try Again Later");
        }
    }
}
