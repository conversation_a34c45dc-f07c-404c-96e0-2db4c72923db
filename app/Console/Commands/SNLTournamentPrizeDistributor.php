<?php

namespace App\Console\Commands;

use Exception;
use App\Models\Leaderboard;
use Illuminate\Console\Command;
use App\Models\LeaderboardPrize;
use App\Models\MasterLeaderboard;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Constants\LeaderboardConstant;
use App\Constants\SLTournamentConstant;
use App\Http\Resources\SNLTournamentResource;
use App\Models\helpdesk\MasterTransactionHistory;
use App\Models\UserPoint;
use App\Jobs\KinesisStreamDataPushJob;
use App\Jobs\SendSNLCancelledCTEvent;
use App\Jobs\SendSNLWinningsCTEvent;
use App\Jobs\SNLCreditApiCallJob;
use App\Models\CampaignToUser;
use App\Models\MasterSNLTournament;
use App\Models\MasterTransactionHistoryFppBonus;
use App\Models\MasterTransactionHistoryRewardpoints;
use App\Models\SNLTournament;
use App\Models\SNLTournamentPrize;
use App\Models\SNLTournamentScore;
use App\Models\Stake;
use App\Models\User;
use App\Models\Vendor;
use App\Traits\Models\common_methods;
use Carbon\Carbon;

class SNLTournamentPrizeDistributor extends Command
{
    use common_methods;
    private $tournamentIdentifier = "PSNL";
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'snltournamentprizedistributor:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Responsible for distributing Prize';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $fifteenMinGap = Carbon::now()->subMinutes(15)->format('Y-m-d H:i:s');
        $tournaments = SNLTournament::where(function($query)use ($fifteenMinGap){
            $query->where(function($query) use ($fifteenMinGap){
                $query->completedTournaments()->where("END_DATE", "<=", $fifteenMinGap);
            })
            ->orWhere(function($query) use ($fifteenMinGap){
                $query->FailedTournaments()->where("END_DATE", "<=", $fifteenMinGap);
            })
            ->orWhere(function ($query){
                $query->activeTournaments()->whereColumn('TOTAL_ENTRIES', '=', 'CURRENT_ENTRIES')
                    ->Where('UPDATED_AT', "<=", Carbon::now()->subMinutes(20)->format('Y-m-d H:i:s'));
            });
        })
        ->where('IS_ACTIVE', 1)
        ->get();

        $vendor = Vendor::where('vendor_id', 15)->first();
        $vendorOtherData = !empty($vendor) ? json_decode($vendor->vendor_partner_other_data, true) : [];
        $baseUrl = !empty($vendorOtherData) ? $vendorOtherData['game_service_base_url'] : null;

        foreach ($tournaments as $tournament) {
            try {
                \Log::info(['tid' => $tournament->SNL_TOURNAMENT_ID]);
                if(!empty($baseUrl)){ 
                    $data = [
                        "tournamentId" => $tournament->SNL_TOURNAMENT_ID
                    ]; 
                    $apiResponse = $this->ApiCallCurl([
                        "url" => $baseUrl."/game/snake-and-ladder/sync-game-data",
                        "form_params" => $data,
                        "headers" => [
                            "Content-Type" => "application/json"
                        ]
                    ]); 
                    if ($apiResponse->httpStatus != 500 && !empty($response = $apiResponse->response)) {
                        if ($response->status == "success") {
                            $this->distributePrize($tournament);
                        } else {
                            Log::error("SNL redis db sync api call error: " . json_encode($apiResponse));
                        }
                    } else {
                        Log::error("SNL redis db sync api call error: " . json_encode($apiResponse));
                    }
                }
            }
            catch(Exception $ex){
                Log::error("Error While Prize Distributor Process for Tournament: $tournament->SNL_TOURNAMENT_ID " . date('Y-m-d H:i:s') . $ex);
            }
        }
    }

    public function distributePrize($tournament){
        
        try {
            DB::beginTransaction();
                /**
                 * Get Reward Type Data
                 */
                $rewardTypeData = \DB::table('reward_types')
                    ->where('REWARD_TYPE_ID', $tournament->REWARD_TYPE_ID)
                    ->first();

                $prizeType = $rewardTypeData->COIN_TYPE_ID;
                $amountType = $rewardTypeData->BALANCE_TYPE_ID;
                $numberOfWinners = $tournament->NUMBER_OF_WINNERS;
                
                $tournamentRank = SNLTournamentScore::where('TOURNAMENT_ID', $tournament->SNL_TOURNAMENT_ID)
                                ->with('user')
                                ->orderBy('TOTAL_SCORE', 'DESC')
                                ->orderBy('GAME_PLAY_ID', 'ASC')
                                ->get();
                if ($tournamentRank->count() > 0) {
                    $topWinners = SNLTournamentResource::customCollection($tournamentRank,$tournamentRank->count(),$numberOfWinners,$tournament->TOTAL_PRIZE_POOL, $tournament);
                    if (count($topWinners) > 0) {
                        foreach ($topWinners as $winningUser) {
                            $internalReferenceNumber = $this->createReferenceNumber($winningUser->USER_ID);
                            $snlTournamentPrize = null;
                            \Log::info([
                                'Current Time' => Carbon::now()->format('Y-m-d H:i:s'), 
                                'SNL_TOURNAMENT_ID' => $tournament->SNL_TOURNAMENT_ID, 
                                'GAME_PLAY_ID' => $winningUser->GAME_PLAY_ID
                            ]);
                            if($tournament->CURRENT_ENTRIES >= $tournament->MINIMUM_ENTRIES){
                               // Create Tournament Prize
                                $snlTournamentPrize = SNLTournamentPrize::create([
                                    'SNL_TOURNAMENT_ID'=> $tournament->SNL_TOURNAMENT_ID,
                                    'GAME_PLAY_ID' => $winningUser->GAME_PLAY_ID,
                                    'USER_ID'   => $winningUser->USER_ID,
                                    'RANKS'  => $winningUser->RANK,
                                    'PRIZE_TYPE' => $prizeType,
                                    'PRIZE_AMOUNT' => $winningUser->PRIZE_MONEY,
                                    'INTERNAL_REFERENCE_NO' => $internalReferenceNumber,
                                    'CT_STATUS' => 0,
                                    'PARTNER_ID' => $winningUser->PARTNER_ID,
                                    'RAKE' => $winningUser->RAKE
                                ]);
                            }

                            switch($prizeType) {
                                case 3:
                                    $pType = "SB";
                                    break;
                                case 12:
                                    $pType = "RP";
                                    break;
                                default:
                                    $pType = $amountType == 2 ? "RCB" : "WIN";
                            }

                            if($tournament->CURRENT_ENTRIES >= $tournament->MINIMUM_ENTRIES){
                                $payload[] = [
                                    'ts' => strtotime(date('Y-m-d H:i:s')),
                                    'rake' => $winningUser->RAKE,
                                    'rank' => $winningUser->RANK,
                                    'type' => 'CREDIT',
                                    'score' => $winningUser->TOTAL_SCORE,
                                    'total' => $winningUser->PRIZE_MONEY,
                                    'txnId' => $winningUser->TRANSACTION_ID,
                                    'amount' => $tournament->BUY_IN,
                                    'prizeType' => $pType,
                                    'gameId' => '15',
                                    'roomId' => $winningUser->SESSION_ID,
                                    'userId' => $winningUser->USER_ID,
                                    'winner' => $winningUser->WINNER,
                                    'matchId' => $winningUser->SESSION_ID,
                                    'winnings' => $winningUser->PRIZE_MONEY - $tournament->BUY_IN,
                                    'tableType' => "Tournament",
                                    'noOfWinners' => $tournament->NUMBER_OF_WINNERS,
                                    'gameTotalPrize' => $winningUser->PRIZE_POOL,
                                    'tournamentId' => $winningUser->TOURNAMENT_ID,
                                    'miniGameType' => 'power',
                                    'snlTournamentPrizeId' => !empty($snlTournamentPrize) ? $snlTournamentPrize->PRIZE_ID : null
                                ];
                            } else {
                                $payload[] = [
                                    'session_id' => $winningUser->SESSION_ID,
                                    'total' => $winningUser->PRIZE_MONEY,
                                    'prizeType' => $pType,
                                    'roomId' => $winningUser->SESSION_ID,
                                    'userId' => $winningUser->USER_ID,
                                    'matchId' => $winningUser->SESSION_ID,
                                    'tournamentId' => $winningUser->TOURNAMENT_ID,
                                    'transactionType' => "REFUND",
                                    'snlTournamentPrizeId' => null
                                ];
                            }
                            
                        }
                        $batchSizeForSNLCreditApiCall = config('rummy_config.BATCH_SIZE_SNL_CREDIT_API_CALL');
                        while(count($payload)) {
                            $data = array_splice($payload, 0, $batchSizeForSNLCreditApiCall);
                            SNLCreditApiCallJob::dispatch($data);
                        }
                    }
                }
                if($tournament->CURRENT_ENTRIES >= $tournament->MINIMUM_ENTRIES){
                    $this->markTournamentCompleted($tournament);
                } else {
                    $this->markTournamentCancelled($tournament);
                }
            DB::commit();
        }
        catch(Exception $ex){
            DB::rollBack();
            SNLTournament::setFailed($tournament->SNL_TOURNAMENT_ID, SLTournamentConstant::STATUS_FAILED);
            Log::error("Error While Distributing the Prize - " . date('Y-m-d H:i:s') . " - For Tournament ID " . $tournament->SNL_TOURNAMENT_ID . "; Exception :" . $ex);
        }
    }

    private function getDistributedMoney($totaWinners = 1, $totalPrizePool){
        return $totalPrizePool / ($totaWinners);
    }

    private function createReferenceNumber($userId){
        return $this->tournamentIdentifier . $userId . date('dmyhis') . rand(0,9999);
    }
    private function markTournamentCompleted($tournament){
        if($tournament->INTERVAL_TYPE == SLTournamentConstant::INTERVAL_CUSTOM){
            MasterSNLTournament::setStatus($tournament->MASTER_SNL_TOURNAMENT_ID, SLTournamentConstant::STATUS_PRIZE_DISTRIBUTED);
            MasterSNLTournament::setCompleted($tournament->MASTER_SNL_TOURNAMENT_ID, SLTournamentConstant::COMPLETED);
        }
        SNLTournament::setStatus($tournament->SNL_TOURNAMENT_ID, SLTournamentConstant::STATUS_PRIZE_DISTRIBUTED);
        SNLTournament::setCompleted($tournament->SNL_TOURNAMENT_ID, SLTournamentConstant::COMPLETED);
        Log::info(['Tournament Status Updated to prize distributed -'.$tournament->SNL_TOURNAMENT_ID]);
        SendSNLWinningsCTEvent::dispatchNow($tournament->SNL_TOURNAMENT_ID);
    }

    private function markTournamentCancelled($tournament){
        if($tournament->INTERVAL_TYPE == SLTournamentConstant::INTERVAL_CUSTOM){
            MasterSNLTournament::setStatus($tournament->MASTER_SNL_TOURNAMENT_ID, SLTournamentConstant::STATUS_CANCELLED);
            MasterSNLTournament::setCompleted($tournament->MASTER_SNL_TOURNAMENT_ID, SLTournamentConstant::COMPLETED);
        }
        SNLTournament::setStatus($tournament->SNL_TOURNAMENT_ID, SLTournamentConstant::STATUS_CANCELLED);
        SNLTournament::setCompleted($tournament->SNL_TOURNAMENT_ID, SLTournamentConstant::COMPLETED);
        Log::info(['Tournament Status Updated to cancelled -'.$tournament->SNL_TOURNAMENT_ID]);
        SendSNLCancelledCTEvent::dispatchNow($tournament->SNL_TOURNAMENT_ID);
    }
}
